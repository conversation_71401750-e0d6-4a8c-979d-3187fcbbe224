#!/usr/bin/env dart

import 'dart:io';
import 'dart:math';
import '../lib/src/core/services/pocketbase_service.dart';
import '../lib/src/core/services/logger_service.dart';

/// <PERSON>ript to create test users and update them with random created/updated dates
/// This helps populate the database with realistic test data for development

void main() async {
  LoggerService.info('🚀 Starting users random dates update script...');

  try {
    // Initialize PocketBase service
    final pbService = PocketBaseService();
    await pbService.initialize();

    LoggerService.info('📡 Connected to PocketBase');

    // Create test users first
    // await createTestUsers(pbService);

    // Update users with random dates
    await updateUsersWithRandomDates(pbService);

    LoggerService.info('✅ Script completed successfully!');
  } catch (e) {
    LoggerService.error('❌ Script failed', e);
    exit(1);
  }
}

/// Create test users with different user types
Future<void> createTestUsers(PocketBaseService pbService) async {
  LoggerService.info('👥 Creating test users...');

  final testUsers = [
    {
      'email': '<EMAIL>',
      'password': 'admin123456',
      'passwordConfirm': 'admin123456',
      'user_type': 'admin',
      'name': 'System Administrator',
      'first_name': 'System',
      'last_name': 'Administrator',
      'status': 'approved',
      'verified': true,
    },
    {
      'email': '<EMAIL>',
      'password': 'solicitor123',
      'passwordConfirm': 'solicitor123',
      'user_type': 'solicitor',
      'name': 'John Smith',
      'first_name': 'John',
      'last_name': 'Smith',
      'law_firm_name': 'Smith & Associates',
      'sra_number': 'SRA123456',
      'position': 'Senior Partner',
      'status': 'approved',
      'verified': true,
    },
    {
      'email': '<EMAIL>',
      'password': 'solicitor123',
      'passwordConfirm': 'solicitor123',
      'user_type': 'solicitor',
      'name': 'Sarah Johnson',
      'first_name': 'Sarah',
      'last_name': 'Johnson',
      'law_firm_name': 'Legal Corp Ltd',
      'sra_number': 'SRA789012',
      'position': 'Partner',
      'status': 'approved',
      'verified': true,
    },
    {
      'email': '<EMAIL>',
      'password': 'cofunder123',
      'passwordConfirm': 'cofunder123',
      'user_type': 'co_funder',
      'name': 'Michael Brown',
      'first_name': 'Michael',
      'last_name': 'Brown',
      'level': '4',
      'status': 'approved',
      'verified': true,
    },
    {
      'email': '<EMAIL>',
      'password': 'cofunder123',
      'passwordConfirm': 'cofunder123',
      'user_type': 'co_funder',
      'name': 'Emma Wilson',
      'first_name': 'Emma',
      'last_name': 'Wilson',
      'level': '3',
      'status': 'approved',
      'verified': true,
    },
    {
      'email': '<EMAIL>',
      'password': 'claimant123',
      'passwordConfirm': 'claimant123',
      'user_type': 'claimant',
      'name': 'David Davis',
      'first_name': 'David',
      'last_name': 'Davis',
      'status': 'approved',
      'verified': true,
    },
    {
      'email': '<EMAIL>',
      'password': 'claimant123',
      'passwordConfirm': 'claimant123',
      'user_type': 'claimant',
      'name': 'Lisa Anderson',
      'first_name': 'Lisa',
      'last_name': 'Anderson',
      'status': 'pending',
      'verified': false,
    },
  ];

  for (final userData in testUsers) {
    try {
      // Check if user already exists
      final existingUsers = await pbService.getList(
        collectionName: 'users',
        filter: 'email = "${userData['email']}"',
        perPage: 1,
      );

      if (existingUsers.items.isNotEmpty) {
        LoggerService.info('   ⚠️  User already exists: ${userData['email']}');
        continue;
      }

      // Create the user
      final record = await pbService.createRecord(
        collectionName: 'users',
        data: userData,
      );

      LoggerService.info(
        '   ✅ Created user: ${userData['email']} (${record.id})',
      );
    } catch (e) {
      LoggerService.error(
        '   ❌ Failed to create user: ${userData['email']}',
        e,
      );
    }
  }
}

/// Update all users with random created and updated dates
Future<void> updateUsersWithRandomDates(PocketBaseService pbService) async {
  LoggerService.info('📅 Updating users with random dates...');

  try {
    // Get all users
    final users = await pbService.getList(
      collectionName: 'users',
      perPage: 100, // Adjust if you have more users
    );

    if (users.items.isEmpty) {
      LoggerService.info('   ⚠️  No users found to update');
      return;
    }

    LoggerService.info('   📊 Found ${users.items.length} users to update');

    final random = Random();
    final now = DateTime.now();

    for (final user in users.items) {
      try {
        // Generate random dates
        final randomDates = generateRandomDates(random, now);

        // Update the user record with random dates
        await pbService.updateRecord(
          collectionName: 'users',
          recordId: user.id,
          data: {
            'created': randomDates['created']!.toIso8601String(),
            'updated': randomDates['updated']!.toIso8601String(),
          },
        );

        LoggerService.info(
          '   ✅ Updated ${user.data['email']}: '
          'created=${randomDates['created']!.toIso8601String().substring(0, 10)}, '
          'updated=${randomDates['updated']!.toIso8601String().substring(0, 10)}',
        );
      } catch (e) {
        LoggerService.error('   ❌ Failed to update user ${user.id}', e);
      }
    }

    LoggerService.info('✅ Finished updating users with random dates');
  } catch (e) {
    LoggerService.error('❌ Failed to update users with random dates', e);
    rethrow;
  }
}

/// Generate random created and updated dates
/// Created date will be between 6 months ago and 1 month ago
/// Updated date will be between created date and now
Map<String, DateTime> generateRandomDates(Random random, DateTime now) {
  // Created date: between 6 months ago and 1 month ago
  final sixMonthsAgo = now.subtract(const Duration(days: 180));
  final oneMonthAgo = now.subtract(const Duration(days: 30));

  final createdRange =
      oneMonthAgo.millisecondsSinceEpoch - sixMonthsAgo.millisecondsSinceEpoch;
  final createdTimestamp =
      sixMonthsAgo.millisecondsSinceEpoch + random.nextInt(createdRange);
  final createdDate = DateTime.fromMillisecondsSinceEpoch(createdTimestamp);

  // Updated date: between created date and now
  final updatedRange =
      now.millisecondsSinceEpoch - createdDate.millisecondsSinceEpoch;
  final updatedTimestamp =
      createdDate.millisecondsSinceEpoch + random.nextInt(updatedRange);
  final updatedDate = DateTime.fromMillisecondsSinceEpoch(updatedTimestamp);

  return {'created': createdDate, 'updated': updatedDate};
}
